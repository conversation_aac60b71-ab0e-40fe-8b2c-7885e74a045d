package cn.iocoder.yudao.module.business.service.certificateinfo;

import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.business.dal.dataobject.certificateinfo.CertificateChainRecordDO;
import cn.iocoder.yudao.module.business.dal.dataobject.certificateinfo.CertificateInfoDO;
import cn.iocoder.yudao.module.business.dal.dataobject.identify.IdentityBlockchainLinkDO;
import cn.iocoder.yudao.module.business.dal.mysql.certificateinfo.CertificateChainRecordMapper;
import cn.iocoder.yudao.module.business.dal.mysql.certificateinfo.CertificateInfoMapper;
import cn.iocoder.yudao.module.business.dal.mysql.identify.IdentityBlockchainLinkMapper;
import cn.iocoder.yudao.module.business.enums.BusinessTypeEnum;
import cn.iocoder.yudao.module.business.enums.CertificateReviewStatusEnum;
import cn.iocoder.yudao.module.business.service.certificateinfo.impl.CertificateInfoAutoChainServiceImpl;
import cn.iocoder.yudao.module.business.service.nft.BusinessNftIssueService;
import cn.iocoder.yudao.module.business.service.blockchainlink.BusinessBlockchainLinkService;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 数据资产存证自动上链服务测试
 *
 * <AUTHOR>
 */
public class CertificateInfoAutoChainServiceTest extends BaseDbUnitTest {

    @InjectMocks
    private CertificateInfoAutoChainServiceImpl certificateInfoAutoChainService;

    @Mock
    private CertificateInfoMapper certificateInfoMapper;

    @Mock
    private IdentityBlockchainLinkMapper identityBlockchainLinkMapper;

    @Mock
    private CertificateChainRecordMapper certificateChainRecordMapper;

    @Mock
    private BusinessNftIssueService businessNftIssueService;

    @Mock
    private BusinessBlockchainLinkService businessBlockchainLinkService;

    @Test
    public void testHandleCertificateReviewStatusChange_Approved() {
        // 准备测试数据
        Integer certificateInfoId = 1;
        String reviewStatus = CertificateReviewStatusEnum.APPROVED.getStatus();

        // 模拟未上链状态
        when(certificateChainRecordMapper.selectSuccessByCertificateInfoId(certificateInfoId))
                .thenReturn(null);

        // 模拟业务区块链链接服务返回空列表（未上链）
        when(businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
                eq(certificateInfoId.longValue()),
                eq(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode())))
                .thenReturn(java.util.Collections.emptyList());

        // 模拟数据资产存证信息
        CertificateInfoDO certificateInfo = new CertificateInfoDO();
        certificateInfo.setId(certificateInfoId);
        certificateInfo.setCreator("123");
        certificateInfo.setDataName("测试数据");
        when(certificateInfoMapper.selectById(certificateInfoId))
                .thenReturn(certificateInfo);

        // 模拟区块链链接信息
        IdentityBlockchainLinkDO blockchainLink = new IdentityBlockchainLinkDO();
        blockchainLink.setAddress("0x1234567890abcdef");
        when(identityBlockchainLinkMapper.selectValidByIdentifyId(123L))
                .thenReturn(blockchainLink);

        // 模拟NFT发行成功
        Map<String, String> nftResult = new HashMap<>();
        nftResult.put("blockchainId", "12345");
        nftResult.put("transactionHash", "0xabcdef123456");
        when(businessNftIssueService.issueNftForBusiness(
                eq(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE), 
                eq(certificateInfoId.longValue()), 
                eq("0x1234567890abcdef")))
                .thenReturn(nftResult);

        // 执行测试
        certificateInfoAutoChainService.handleCertificateReviewStatusChange(certificateInfoId, reviewStatus);

        // 验证NFT发行服务被调用
        verify(businessNftIssueService, times(1))
                .issueNftForBusiness(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE, 
                        certificateInfoId.longValue(), "0x1234567890abcdef");

        // 验证上链记录被创建和更新
        verify(certificateChainRecordMapper, times(1)).insert(any(CertificateChainRecordDO.class));
        verify(certificateChainRecordMapper, times(1)).updateById(any(CertificateChainRecordDO.class));
    }

    @Test
    public void testHandleCertificateReviewStatusChange_NotApproved() {
        // 准备测试数据
        Integer certificateInfoId = 1;
        String reviewStatus = CertificateReviewStatusEnum.PENDING.getStatus();

        // 执行测试
        certificateInfoAutoChainService.handleCertificateReviewStatusChange(certificateInfoId, reviewStatus);

        // 验证NFT发行服务不会被调用
        verify(businessNftIssueService, never())
                .issueNftForBusiness(any(), any(), any());
    }

    @Test
    public void testHandleCertificateReviewStatusChange_AlreadyOnChain() {
        // 准备测试数据
        Integer certificateInfoId = 1;
        String reviewStatus = CertificateReviewStatusEnum.APPROVED.getStatus();

        // 模拟已上链状态
        CertificateChainRecordDO existingRecord = new CertificateChainRecordDO();
        existingRecord.setNftIssueStatus("S");
        when(certificateChainRecordMapper.selectSuccessByCertificateInfoId(certificateInfoId))
                .thenReturn(existingRecord);

        // 模拟业务区块链链接服务返回非空列表（已上链）
        when(businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
                eq(certificateInfoId.longValue()),
                eq(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode())))
                .thenReturn(java.util.Arrays.asList(new cn.iocoder.yudao.module.extend.entity.copyright.BusinessBlockchainLinkDO()));

        // 执行测试
        certificateInfoAutoChainService.handleCertificateReviewStatusChange(certificateInfoId, reviewStatus);

        // 验证NFT发行服务不会被调用
        verify(businessNftIssueService, never())
                .issueNftForBusiness(any(), any(), any());
    }

    @Test
    public void testIssueNftForCertificate_Success() {
        // 准备测试数据
        Integer certificateInfoId = 1;

        // 模拟数据资产存证信息
        CertificateInfoDO certificateInfo = new CertificateInfoDO();
        certificateInfo.setId(certificateInfoId);
        certificateInfo.setCreator("123");
        certificateInfo.setDataName("测试数据");
        when(certificateInfoMapper.selectById(certificateInfoId))
                .thenReturn(certificateInfo);

        // 模拟区块链链接信息
        IdentityBlockchainLinkDO blockchainLink = new IdentityBlockchainLinkDO();
        blockchainLink.setAddress("0x1234567890abcdef");
        when(identityBlockchainLinkMapper.selectValidByIdentifyId(123L))
                .thenReturn(blockchainLink);

        // 模拟NFT发行成功
        Map<String, String> nftResult = new HashMap<>();
        nftResult.put("blockchainId", "12345");
        nftResult.put("transactionHash", "0xabcdef123456");
        when(businessNftIssueService.issueNftForBusiness(
                eq(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE), 
                eq(certificateInfoId.longValue()), 
                eq("0x1234567890abcdef")))
                .thenReturn(nftResult);

        // 执行测试
        String result = certificateInfoAutoChainService.issueNftForCertificate(certificateInfoId);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.contains("12345"));
        assertTrue(result.contains("0xabcdef123456"));

        // 验证NFT发行服务被调用
        verify(businessNftIssueService, times(1))
                .issueNftForBusiness(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE, 
                        certificateInfoId.longValue(), "0x1234567890abcdef");
    }

    @Test
    public void testIssueNftForCertificate_CertificateNotFound() {
        // 准备测试数据
        Integer certificateInfoId = 1;

        // 模拟数据资产存证不存在
        when(certificateInfoMapper.selectById(certificateInfoId))
                .thenReturn(null);

        // 执行测试并验证异常
        assertThrows(IllegalArgumentException.class, () -> {
            certificateInfoAutoChainService.issueNftForCertificate(certificateInfoId);
        });

        // 验证NFT发行服务不会被调用
        verify(businessNftIssueService, never())
                .issueNftForBusiness(any(), any(), any());
    }

    @Test
    public void testIsCertificateOnChain_True() {
        // 准备测试数据
        Integer certificateInfoId = 1;

        // 模拟业务区块链链接服务返回非空列表（已上链）
        when(businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
                eq(certificateInfoId.longValue()),
                eq(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode())))
                .thenReturn(java.util.Arrays.asList(new cn.iocoder.yudao.module.extend.entity.copyright.BusinessBlockchainLinkDO()));

        // 执行测试
        boolean result = certificateInfoAutoChainService.isCertificateOnChain(certificateInfoId);

        // 验证结果
        assertTrue(result);
    }

    @Test
    public void testIsCertificateOnChain_False() {
        // 准备测试数据
        Integer certificateInfoId = 1;

        // 模拟业务区块链链接服务返回空列表（未上链）
        when(businessBlockchainLinkService.getBlockchainLinksByBusinessIdAndType(
                eq(certificateInfoId.longValue()),
                eq(BusinessTypeEnum.DATA_CERTIFICATE_EVIDENCE.getCode())))
                .thenReturn(java.util.Collections.emptyList());

        // 执行测试
        boolean result = certificateInfoAutoChainService.isCertificateOnChain(certificateInfoId);

        // 验证结果
        assertFalse(result);
    }
}
